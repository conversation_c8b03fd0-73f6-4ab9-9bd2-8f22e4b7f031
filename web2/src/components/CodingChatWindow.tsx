'use client';

import { type Message } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useState, useRef, useCallback } from 'react';
import type { FormEvent, ReactNode } from 'react';
import { toast, Toaster } from 'sonner';
import { StickToBottom, useStickToBottomContext } from 'use-stick-to-bottom';
import { ArrowDown, ArrowUpIcon, LoaderCircle, Paperclip, X } from 'lucide-react';

import { ChatMessageBubble } from '@/components/ChatMessageBubble';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/utils/cn';

interface FileAttachment {
  id: string;
  name: string;
  content: string;
  type: string;
}

function ChatMessages(props: {
  messages: Message[];
  emptyStateComponent: ReactNode;
  aiEmoji?: string;
  className?: string;
}) {
  return (
    <div className="flex flex-col max-w-[800px] mx-auto pb-12 w-full">
      {props.messages.map((m, i) => {
        return <ChatMessageBubble key={m.id} message={m} aiEmoji={props.aiEmoji} />;
      })}
    </div>
  );
}

function ScrollToBottom(props: { className?: string }) {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  if (isAtBottom) return null;
  return (
    <Button variant="outline" className={props.className} onClick={() => scrollToBottom()}>
      <ArrowDown className="w-4 h-4" />
      <span>Scroll to bottom</span>
    </Button>
  );
}

function FileUploadArea(props: {
  files: FileAttachment[];
  onFileAdd: (file: FileAttachment) => void;
  onFileRemove: (id: string) => void;
  className?: string;
}) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    Array.from(files).forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const fileAttachment: FileAttachment = {
          id: Math.random().toString(36).substr(2, 9),
          name: file.name,
          content: content,
          type: file.type
        };
        props.onFileAdd(fileAttachment);
      };
      reader.readAsText(file);
    });
  }, [props]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  return (
    <div className={props.className}>
      {props.files.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-2">
          {props.files.map(file => (
            <div key={file.id} className="flex items-center gap-1 bg-gray-100 rounded px-2 py-1 text-sm">
              <span className="truncate max-w-[150px]">{file.name}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0"
                onClick={() => props.onFileRemove(file.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
      
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-4 text-center transition-colors",
          dragOver ? "border-blue-500 bg-blue-50" : "border-gray-300",
          props.className
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={(e) => handleFileSelect(e.target.files)}
          accept=".txt,.js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.h,.css,.html,.json,.xml,.md,.sql,.sh,.yaml,.yml"
        />
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          className="gap-2"
        >
          <Paperclip className="h-4 w-4" />
          选择文件或拖拽到此处
        </Button>
        <p className="text-xs text-gray-500 mt-2">
          支持代码文件、文档等文本文件
        </p>
      </div>
    </div>
  );
}

function CodingChatInput(props: {
  onSubmit: (e: FormEvent<HTMLFormElement>) => void;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  loading?: boolean;
  placeholder?: string;
  files: FileAttachment[];
  onFileAdd: (file: FileAttachment) => void;
  onFileRemove: (id: string) => void;
  className?: string;
}) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      props.onSubmit(e as unknown as FormEvent<HTMLFormElement>);
    }
  };

  return (
    <form
      onSubmit={(e) => {
        e.stopPropagation();
        e.preventDefault();
        props.onSubmit(e);
      }}
      className={cn('flex w-full flex-col', props.className)}
    >
      <FileUploadArea
        files={props.files}
        onFileAdd={props.onFileAdd}
        onFileRemove={props.onFileRemove}
        className="mb-2"
      />
      
      <div className="bg-background rounded-lg flex items-center max-w-[800px] w-full mx-auto px-2 pr-2 border border-gray-300">
        <Textarea
          value={props.value}
          placeholder={props.placeholder}
          onChange={props.onChange}
          onKeyDown={handleKeyDown}
          className="border-none outline-none bg-transparent p-4 flex-grow resize-none overflow-y-auto min-h-[50px]"
          rows={1}
          style={{ maxHeight: '200px', minHeight: '100px' }}
        />

        <div className="flex items-center gap-3">
          <Button
            className="rounded-full p-1.5 h-fit"
            type="submit"
            disabled={props.loading}
          >
            {props.loading ? <LoaderCircle className="animate-spin" /> : <ArrowUpIcon size={14} />}
          </Button>
        </div>
      </div>
    </form>
  );
}

function StickyToBottomContent(props: {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}) {
  const context = useStickToBottomContext();

  return (
    <div
      ref={context.scrollRef}
      style={{ width: '100%', height: '100%' }}
      className={cn('grid grid-rows-[1fr,auto]', props.className)}
    >
      <div ref={context.contentRef} className={props.contentClassName}>
        {props.content}
      </div>

      {props.footer}
    </div>
  );
}

export function CodingChatWindow(props: {
  endpoint: string;
  emptyStateComponent: ReactNode;
  placeholder?: string;
  emoji?: string;
}) {
  const [files, setFiles] = useState<FileAttachment[]>([]);
  
  const chat = useChat({
    api: props.endpoint,
    body: {
      files: files
    },
    onFinish(response: Message) {
      console.log('Final response: ', response?.content);
    },
    onResponse(response: Response) {
      console.log('Response received. Status:', response.status);
    },
    onError: (e: Error) => {
      console.error('Error: ', e);
      toast.error(`Error while processing your request`, { description: e.message });
    },
  });

  const handleFileAdd = useCallback((file: FileAttachment) => {
    setFiles(prev => [...prev, file]);
  }, []);

  const handleFileRemove = useCallback((id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  }, []);

  function isChatLoading(): boolean {
    return chat.status === 'streaming';
  }

  async function sendMessage(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (isChatLoading()) return;
    chat.handleSubmit(e);
  }

  return (
    <>
      <StickToBottom>
        <StickyToBottomContent
          className="absolute inset-0"
          contentClassName="py-8 px-2"
          content={
            chat.messages.length === 0 ? (
              <div>{props.emptyStateComponent}</div>
            ) : (
              <ChatMessages
                aiEmoji={props.emoji}
                messages={chat.messages}
                emptyStateComponent={props.emptyStateComponent}
              />
            )
          }
          footer={
            <div className="sticky bottom-8">
              <ScrollToBottom className="absolute bottom-full left-1/2 -translate-x-1/2 mb-4" />
              <CodingChatInput
                value={chat.input}
                onChange={chat.handleInputChange}
                onSubmit={sendMessage}
                loading={isChatLoading()}
                placeholder={props.placeholder ?? 'What can I help you with?'}
                files={files}
                onFileAdd={handleFileAdd}
                onFileRemove={handleFileRemove}
              />
            </div>
          }
        />
      </StickToBottom>
      <Toaster />
    </>
  );
}
