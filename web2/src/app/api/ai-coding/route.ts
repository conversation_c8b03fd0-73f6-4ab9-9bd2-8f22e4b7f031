import { NextRequest, NextResponse } from 'next/server';
import { type Message, LangChainAdapter } from 'ai';
import { graph } from "../../../graph/aiCoding"

import { convertVercelMessageToLangChainMessage } from '@/utils/message-converters';
import { logToolCallsInDevelopment } from '@/utils/stream-logging';

/**
 * This handler initializes and calls an AI coding agent.
 * It supports file uploads and coding-related tasks.
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('AI Coding API: 收到请求', {
      messagesCount: body.messages?.length || 0,
      hasMessages: !!body.messages,
      hasFiles: !!body.files && body.files.length > 0,
      filesCount: body.files?.length || 0
    });

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */
    const messages = (body.messages ?? [])
      .filter((message: Message) => message.role === 'user' || message.role === 'assistant')
      .map(convertVercelMessageToLangChainMessage);

    console.log('AI Coding API: 处理后的消息', {
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content
    });

    // 获取最后一条用户消息作为输入
    const lastUserMessage = messages
      .filter((msg: any) => msg.getType() === 'human')
      .pop();

    let userInput = (lastUserMessage?.content as string) || '';

    // 处理文件上传
    const files = body.files || [];
    if (files.length > 0) {
      console.log('AI Coding API: 处理上传的文件', { filesCount: files.length });
      
      const fileContents = files.map((file: any) => {
        return `文件名: ${file.name}\n文件类型: ${file.type}\n文件内容:\n\`\`\`\n${file.content}\n\`\`\``;
      }).join('\n\n');

      if (userInput.trim()) {
        userInput = `${userInput}\n\n上传的文件:\n${fileContents}`;
      } else {
        userInput = `请分析以下上传的文件:\n${fileContents}`;
      }
    }

    /**
     * Stream back all generated tokens and steps from their runs.
     */
    const graphInput = {
      messages: messages,
      input: userInput,
      output: '',
      files: files
    };

    console.log('AI Coding API: 调用 graph', {
      input: userInput.substring(0, 200) + (userInput.length > 200 ? '...' : ''),
      messagesCount: messages.length,
      filesCount: files.length
    });

    // 使用 streamEvents 方法
    const eventStream = graph.streamEvents(graphInput, {
      version: 'v2'
    });

    // 应用流式日志处理器
    const transformedStream = logToolCallsInDevelopment(eventStream);

    // Adapt the LangChain stream to Vercel AI SDK Stream
    return LangChainAdapter.toDataStreamResponse(transformedStream);
  } catch (e: any) {
    console.error('AI Coding API: 错误', e);
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}
